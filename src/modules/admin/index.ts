import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { REFIX_MODULE } from '../config-module';
import { PackagePlanController } from './package/package-plan.controller';
import { PackageService } from './package/package-plan.service';
import { TermsConfigController } from './term/term.controller';
import { TermService } from './term/term.service';
import { NewsController } from './news/new-article.controller';
import { NewsService } from './news/new-article.service';
import { MemberService } from './member/member.service';
import { MemberController } from './member/member.controller';
import { AdvisoryService } from './advisory/advisory.service';
import { AdvisoryController } from './advisory/advisory.controller';
import { OrderService } from './order/order.service';
import { OrderController } from './order/order.controller';
import { PaymentService } from './payment/payment.service';
import { PaymentController } from './payment/payment.controller';
import { UploadController } from './upload/upload.controller';
import { AwsModule } from '~/@core/services/aws.module';

const services = [PackageService, TermService, NewsService, MemberService, AdvisoryService, OrderService, PaymentService];
const controllers = [PackagePlanController, TermsConfigController, NewsController, MemberController, AdvisoryController, OrderController, PaymentController, UploadController];

@ChildModule({
  prefix: REFIX_MODULE.admin,
  providers: services,
  controllers: controllers,
  imports: [AwsModule],
})
export class AdminModule implements NestModule {
  configure(consumer: MiddlewareConsumer) { }
}
