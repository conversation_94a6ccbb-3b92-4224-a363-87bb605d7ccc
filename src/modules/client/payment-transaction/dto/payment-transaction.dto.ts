import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";
import { PageRequest } from "~/@systems/utils";
import { NSPackage } from "~/common/enums";
import { NSPayment } from "~/common/enums/payment.enum";

export class CreatePaymentTransactionDto {
    @ApiProperty({ description: 'ID của member thanh toán (member sở hữu thẻ)' })
    @IsUUID()
    memberId: string;

    @ApiProperty({ description: 'Mã code thanh toán' })
    @IsOptional()
    code?: string

    @ApiProperty({ description: 'ID của đơn hàng' })
    @IsUUID()
    orderId: string;

    @ApiProperty({ description: 'Số tiền giao dịch thực tế (bao gồm giảm giá)' })
    @IsNumber()
    amount: number;

    @ApiProperty({ description: 'Tổng số tiền giao dịch (trướ<PERSON>ế<PERSON> mãi)' })
    @IsNumber()
    grossAmount: number;

    @ApiProperty({ description: 'Đ<PERSON>i tác cổng thanh toán' })
    @IsEnum(NSPayment.EPaymentProvider)
    paymentProvider: NSPayment.EPaymentProvider;

    @ApiProperty({ description: 'Phương thức thanh toán' })
    @IsEnum(NSPayment.EPaymentMethod)
    paymentMethod: NSPayment.EPaymentMethod;

    // Tự động gia hạn
    @ApiPropertyOptional({ description: 'Tự động gia hạn' })
    @IsOptional()
    isAutoRenewal?: boolean;

    @ApiPropertyOptional({ description: 'ID giao dịch trên cổng thanh toán' })
    @IsOptional()
    refId?: string;

    @ApiPropertyOptional({ description: 'Client secret của Stripe' })
    @IsOptional()
    clientSecret?: string;

    @ApiProperty({ description: 'MONTHLY/YEARLY' })
    @IsEnum(NSPackage.EPlanTypePayment)
    @IsNotEmpty()
    unit: NSPackage.EPlanTypePayment;
}

export class PaymentTransactionListDto extends PageRequest {
    @ApiProperty({ description: 'Mã code thanh toán' })
    @IsOptional()
    code?: string;

    @ApiProperty({ description: 'ID của member thanh toán (member sở hữu thẻ)' })
    @IsOptional()
    memberId?: string;

    @ApiProperty({ description: 'ID của đơn hàng' })
    @IsOptional()
    orderId?: string;

    @ApiProperty({ description: 'Trạng thái thanh toán' })
    @IsOptional()
    status?: NSPayment.ETransactionStatus;
}

//amount: number, currency = 'usd', orderId: string, transactionId: string
export class PaymentIntentDto {
    @ApiProperty({ description: 'Số tiền giao dịch' })
    @IsNumber()
    amount: number;

    @ApiProperty({ description: 'Đơn vị tiền tệ' })
    @IsString()
    currency: string = 'usd';

    @ApiProperty({ description: 'ID của đơn hàng' })
    @IsUUID()
    orderId: string;

    @ApiProperty({ description: 'ID của giao dịch' })
    @IsUUID()
    transactionId: string;

    @ApiProperty({ description: 'Loại gói dịch vụ' })
    @IsEnum(NSPackage.EPlanTypePayment)
    paymentType: NSPackage.EPlanTypePayment;
}

export class PaymentSubscriptionDto {
    @ApiProperty({ description: 'ID của đơn hàng' })
    @IsUUID()
    orderId: string;

    @ApiProperty({ description: 'ID của giao dịch' })
    @IsUUID()
    transactionId: string;

    @ApiProperty({ description: 'Email của khách hàng' })
    @IsString()
    customerEmail: string;

    @ApiProperty({ description: 'ID của gói dịch vụ' })
    @IsUUID()
    planId: string;
}   