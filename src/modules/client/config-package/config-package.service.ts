import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { ConfigLogDto, ConfigPackageListDto, CreateConfigPackageDto, UpdateConfigPackageDto } from './dto/config-package.dto';
import { ConfigPackageRepo, ConfigPackageDetailRepo } from '~/domains/primary/config-package/config-package.repo';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSConfig } from '~/common/enums/config.enum';
import { ConfigPackageDetailEntity } from '~/domains/primary/config-package/config-package-detail.entity';
import * as dayjs from 'dayjs';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import * as _ from 'lodash';
import { generateCurlText, generatePayloadFields } from '~/common/helpers/generate-text.helper';
import { configEnv } from '~/@config/env';
import { MemberApiLogRepo } from '~/domains/primary/member-api-log/member-api-log.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { memberSessionContext } from '../member-session.context';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class ConfigPackageService {
    constructor(
    ) { }

    @BindRepo(ConfigPackageRepo)
    private configRepo: ConfigPackageRepo;

    @BindRepo(ConfigPackageDetailRepo)
    private detailRepo: ConfigPackageDetailRepo;

    @BindRepo(MemberApiRepo)
    private memberApiRepo: MemberApiRepo;

    @BindRepo(MemberApiLogRepo)
    private memberApiLogRepo: MemberApiLogRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @DefTransaction()
    async create(dto: CreateConfigPackageDto) {
        const { memberId } = memberSessionContext;
        const packagePlanMember = await this.memberPackageRepo.findOne({
            where: {
                id: dto.packagePlanId
            }
        });
        if (!packagePlanMember) throw new BusinessException('Member package not found');
        if (packagePlanMember.currentConfig >= packagePlanMember.initialConfigLimit) {
            throw new BusinessException('You have reached the maximum number of configurations');
        }

        // Update Member Package
        await this.memberPackageRepo.update({
            id: packagePlanMember.id,
        }, {
            currentConfig: packagePlanMember.currentConfig + 1,
        });

        // Tạo config package
        const config = this.configRepo.create({ ...dto, memberId });
        if (!config.code) config.code = generateCodeHelper.generateCode('CP', 8);
        await this.configRepo.save(config);
        const details = dto.fields?.map((detail) => this.detailRepo.create({
            ...detail,
            configPackageId: config.id,
        }));
        if (details?.length) await this.detailRepo.save(details);

        // Gen API
        const apiGen = await this.generateMemberAPIConfig(config.id);

        return { ...config, fields: details || [], apiInfo: apiGen };
    }

    async findPagination(body: ConfigPackageListDto) {
        const { memberId } = memberSessionContext;
        const { code, name, status, ...pageRequest } = body;
        const where: any = { memberId }
        if (code) {
            where.code = code;
        }
        if (name) {
            where.name = name;
        }
        if (status) {
            where.status = status;
        }
        return this.configRepo.findPagination({ where }, pageRequest);
    }

    async findOne(id: string) {
        const config = await this.configRepo.findOne(id);
        if (!config) throw new NotFoundException('Config package not found');
        const details = await this.detailRepo.find({ where: { configPackageId: config.id } });

        const apiInfo = await this.memberApiRepo.findOne({ where: { configId: config.id } });
        return {
            ...config,
            apiInfo,
            fields: details || []
        };
    }

    async findLogs(body: ConfigLogDto) {
        const { configId, ...pageRequest } = body;
        return this.memberApiLogRepo.findPagination({ where: { configId: body.configId } }, pageRequest);
    }

    @DefTransaction()
    async update(dto: UpdateConfigPackageDto) {
        const { id, ...updateDto } = dto;
        const config = await this.findOne(id);
        await this.configRepo.update({
            id,
        }, {
            ...updateDto,
        });

        // Xoá toàn bộ detail cũ nếu có cập nhật
        if (dto.details) {
            await this.detailRepo.delete({ configPackageId: id });
            const newDetails = dto.details.map((detail) =>
                this.detailRepo.create({ ...detail, configPackageId: id }),
            );
            await this.detailRepo.save(newDetails);
        }
        // Gen lại API
        const apiInfo = await this.generateMemberAPIConfig(id);
        return { ...config, apiInfo };
    }

    @DefTransaction()
    async inActive(id: string) {
        const config = await this.findOne(id);
        if (!config) throw new NotFoundException('Config package not found');
        return await this.configRepo.update({ id }, { status: NSConfig.EStatus.INACTIVE });
    }

    @DefTransaction()
    async active(id: string) {
        const config = await this.findOne(id);
        if (!config) throw new NotFoundException('Config package not found');
        return await this.configRepo.update({ id }, { status: NSConfig.EStatus.ACTIVE });
    }

    @DefTransaction()
    async generateMemberAPIConfig(configId: string) {
        const config = await this.configRepo.findOne(configId);
        if (!config) throw new NotFoundException('Config package not found');

        const details = await this.detailRepo.find({ where: { configPackageId: config.id } });
        if (!details || !details.length) {
            throw new BadRequestException('Cấu hình không có field nào');
        }

        const { EXTERNAL_API_HOST, EXTERNAL_API_PATH } = configEnv();

        const path = `${EXTERNAL_API_PATH}/${config.id}`;
        const host = EXTERNAL_API_HOST; // Có thể lấy từ ENV hoặc config
        const fullUrl = `${host}${path}`;
        const method = 'POST';

        const samplePayload = generatePayloadFields(details);
        const curlText = generateCurlText(fullUrl, samplePayload, method);

        await this.memberApiRepo.delete({ configId });

        const memberApi = this.memberApiRepo.create({
            configId: config.id,
            memberId: config.memberId,
            host,
            path,
            method,
            body: samplePayload,
            curlText,
        });

        const data = await this.memberApiRepo.save(memberApi);
        return data;
    }

    @DefTransaction()
    async handleIncomingExternalApi(body: any, configId: string) {
        const config = await this.configRepo.findOne(configId);
        if (!config) throw new NotFoundException('Config package not found');

        const details = await this.detailRepo.find({ where: { configPackageId: config.id } });
        if (!details || !details.length) {
            throw new BadRequestException('Missing config details');
        }

        const memberAPI = await this.memberApiRepo.findOne({ where: { configId } });
        if (!memberAPI) throw new NotFoundException('Member API not found');

        const packagePlanMember = await this.memberPackageRepo.findOne({ where: { id: config.packagePlanId } });
        if (!packagePlanMember) throw new NotFoundException('Member package not found');
        if (packagePlanMember.status !== 'ACTIVE') {
            throw new BadRequestException('Package is not active or expired');
        }

        const mapped = await this.validateOrHandleIncomingData(body, details);

        const newLog = this.memberApiLogRepo.create({
            configId,
            memberId: config.memberId,
            memberPackageId: packagePlanMember.id, // Update lại dùng memberContextSession sau
            host: memberAPI.host,
            url: memberAPI.path,
            request: body,
            response: mapped,
            isTest: body.isTest || false,
            method: memberAPI.method,
            statusCode: NSConfig.EApiStatusCode.SUCCESS,
        });
        await this.memberApiLogRepo.save(newLog); // Ghi log handle call api

        if (body.isTest) {
            return {
                message: 'Test OK',
                data: mapped,
            }
        }

        await this.memberPackageRepo.update({
            id: packagePlanMember.id,
        }, {
            currentTransaction: packagePlanMember.currentTransaction + 1,
        });

        if (packagePlanMember.currentTransaction + 1 >= packagePlanMember.initialTransactionLimit) {
            await this.memberPackageRepo.update({
                id: packagePlanMember.id,
            }, {
                status: 'EXPIRED',
            });
        }

        // TODO: Call API to Chain

        return {
            message: 'OK',
            data: mapped,
        }
    }

    private async validateOrHandleIncomingData(body: any, details: ConfigPackageDetailEntity[]) {
        const result: Record<string, any> = {};

        for (const field of details) {
            const value = body[field.mappingField];

            // Nếu thiếu field bắt buộc
            if (field.isRequired && (value === undefined || value === null)) {
                throw new BadRequestException(`Missing required field: ${field.mappingField}`);
            }

            switch (field.type) {
                case 'string':
                    if (typeof value !== 'string') {
                        throw new BadRequestException(`${field.mappingField} must be a string`);
                    }
                    result[field.nameField] = value;
                    break;

                case 'number':
                    if (typeof value !== 'number') {
                        throw new BadRequestException(`${field.mappingField} must be a number`);
                    }
                    result[field.nameField] = value;
                    break;

                case 'boolean':
                    if (typeof value !== 'boolean') {
                        throw new BadRequestException(`${field.mappingField} must be a boolean`);
                    }
                    result[field.nameField] = value;
                    break;

                case 'json':
                    try {
                        const parsed = typeof value === 'object' ? value : JSON.parse(value);
                        result[field.nameField] = parsed;
                    } catch (e) {
                        throw new BadRequestException(`${field.mappingField} must be valid JSON`);
                    }
                    break;

                case 'date':
                    if (!dayjs(value).isValid()) {
                        throw new BadRequestException(`${field.mappingField} must be a valid date`);
                    }
                    result[field.nameField] = dayjs(value).toISOString();
                    break;

                default:
                    throw new BadRequestException(`Unsupported type: ${field.type}`);
            }
        }

        return result;
    }
}
