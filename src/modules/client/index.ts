import { MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { REFIX_MODULE } from '../config-module';
import { MemberAuthService } from './member-auth/member-auth.service';
import { MemberAuthController } from './member-auth/member-auth.controller';
import { ConfigPackageService } from './config-package/config-package.service';
import { ConfigPackageController } from './config-package/config-package.controller';
import { OrderController } from './order/order.controller';
import { OrderService } from './order/order.service';
import { PaymentTransactionController } from './payment-transaction/payment-transaction.controller';
import { PaymentTransactionService } from './payment-transaction/payment-transaction.service';
import { GoogleStrategy } from './@providers/google.strategy';
import { JwtStrategy } from './@providers/jwt.strategy';
import { MemberPackageController } from './member-package/member-package.controller';
import { MemberPackageService } from './member-package/member-package.service';
import { MemberMiddleware } from './member.middleware';
import { PackagePlanController } from './package/package-plan.controller';
import { PackagePlanService } from './package/package-plan.service';
import { PublicService } from './publics/public.service';
import { PublicController } from './publics/public.controller';

@ChildModule({
  prefix: REFIX_MODULE.client,
  providers: [
    GoogleStrategy,
    JwtStrategy,
    MemberAuthService,
    ConfigPackageService,
    OrderService,
    PaymentTransactionService,
    MemberPackageService,
    PackagePlanService,
    PublicService,
  ],
  controllers: [
    MemberAuthController,
    ConfigPackageController,
    OrderController,
    PaymentTransactionController,
    MemberPackageController,
    PackagePlanController,
    PublicController,
  ],
})
export class ClientModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MemberMiddleware)
      .exclude(
        { path: `${REFIX_MODULE.client}/auth/login`, method: RequestMethod.POST },
        { path: `${REFIX_MODULE.client}/auth/google`, method: RequestMethod.POST },
        { path: `${REFIX_MODULE.client}/auth/google/callback`, method: RequestMethod.POST },
        { path: `${REFIX_MODULE.client}/auth/register`, method: RequestMethod.POST },
        { path: `${REFIX_MODULE.client}/package-plans/pagination`, method: RequestMethod.POST },
        { path: `${REFIX_MODULE.client}/package-plans`, method: RequestMethod.GET },
        { path: `${REFIX_MODULE.client}/publics/total-report`, method: RequestMethod.GET },
      )
      .forRoutes({
        path: `${REFIX_MODULE.client}*`,
        method: RequestMethod.ALL,
      });
  }
}
