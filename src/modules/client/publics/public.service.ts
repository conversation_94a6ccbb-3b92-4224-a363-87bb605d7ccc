import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { ConfigPackageRepo, PaymentTransactionRepo } from '~/domains/primary';
import { memberSessionContext } from '../member-session.context';
import { MemberRepo } from '~/domains/primary';

@Injectable()
export class PublicService {
  constructor() {}

  @BindRepo(ConfigPackageRepo)
  private configPackageRepo: ConfigPackageRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  async totalReport() {
    const { memberId } = memberSessionContext;
    const wheres: any = {};
    if (memberId) {
      wheres.memberId = memberId;
    }
    const totalMember = await this.memberRepo.count();
    const totalConfigPackage = await this.configPackageRepo.count();
    const totalTransaction = await this.paymentTransactionRepo.count();
    return {
      totalMember,
      totalTransaction,
      totalConfigPackage,
    };
  }
}
