import { Injectable } from "@nestjs/common";
import { BindRepo, DefTransaction } from "~/@core/decorator";
import { PaymentTransactionRepo } from "~/domains/primary/payment-transaction/payment-transaction.repo";
import { CreatePaymentTransactionDto, PaymentIntentDto, PaymentSubscriptionDto, PaymentTransactionListDto } from "./dto/payment-transaction.dto";
import Stripe from 'stripe';
import { configEnv } from "~/@config/env";
import { NSPayment } from "~/common/enums/payment.enum";
import { OrderRepo } from "~/domains/primary/order/order.repo";
import { NotFoundException } from "@nestjs/common";
import { generateCodeHelper } from "~/common/helpers/generate-code.helper";
import { OrderItemRepo, PackagePlanRepo } from "~/domains/primary/";
import { MemberRepo } from "~/domains/primary/member/member.repo";
import { MemberPackageRepo } from "~/domains/primary/member-package/member-package.repo";
import { In } from "typeorm";

@Injectable()
export class PaymentTransactionService {
    private stripe: Stripe
    constructor(
    ) { this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {}); }

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(OrderRepo)
    private orderRepo: OrderRepo;

    @BindRepo(OrderItemRepo)
    private orderItemsRepo: OrderItemRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @DefTransaction()
    async create(dto: CreatePaymentTransactionDto) {
        const { orderId, memberId, paymentProvider, isAutoRenewal, amount, unit } = dto;
        const [order, planPackage, member] = await Promise.all([
            this.orderRepo.findOne({ where: { id: orderId } }),
            this.orderItemsRepo.findOne({ where: { orderId } }),
            this.memberRepo.findOne({ where: { id: memberId } }),
        ]);

        if (!order) throw new NotFoundException('Order not found');
        if (!planPackage) throw new NotFoundException('Plan package not found');
        if (!member) throw new NotFoundException('Member not found');

        const plan = await this.packagePlanRepo.findOne({ where: { id: planPackage.packagePlanId } });
        if (!plan) throw new NotFoundException('Plan not found');

        const count = await this.paymentTransactionRepo.count();

        // 2. Tạo payment transaction
        const transaction = this.paymentTransactionRepo.create({
            ...dto,
            code: generateCodeHelper.generateSOCode(count, 'PT'), // nên thay bằng UUID nếu cần uniqueness
        });
        const savedTransaction = await this.paymentTransactionRepo.save(transaction);
        const transactionId = savedTransaction.id;

        // 3. Nếu Stripe thì tạo intent hoặc subscription
        if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
            const stripeAmount = amount * 100;
            let clientSecret = '';
            let refId = '';

            if (isAutoRenewal) {
                await this.orderRepo.update({ id: orderId }, { isAutoRenewal: true });

                const subscription: any = await this.createSubscription({
                    orderId,
                    transactionId,
                    customerEmail: member.email,
                    planId: planPackage.packagePlanId,
                });

                clientSecret = subscription?.latest_invoice?.payment_intent?.client_secret ?? '';
                refId = subscription.id;
            } else {
                const paymentIntent = await this.createPaymentIntent({
                    amount: stripeAmount,
                    currency: 'usd',
                    orderId,
                    transactionId,
                    paymentType: dto.unit,
                });

                clientSecret = paymentIntent.client_secret;
                refId = paymentIntent.id;
            }

            // Cập nhật lại transaction
            await this.paymentTransactionRepo.update(transactionId, {
                clientSecret,
                refId,
            });

            // Gán lại dto (trả về client)
            dto.clientSecret = clientSecret;
            dto.refId = refId;
        }

        let _transaction = await this.paymentTransactionRepo.findOne({
            where: {
                orderId,
                memberId,
                status: In(['PENDING']), // tuỳ enum status của bạn
                paymentProvider,
            },
        });

        // 4. Tạo member package
        let newMemberPackage = null;
        if (!_transaction) {
            const memberPackage = this.memberPackageRepo.create({
                memberId,
                orderId,
                packagePlanId: plan.id,
                initialTransactionLimit: plan.transactionLimit,
                currentTransaction: 0,
                initialConfigLimit: plan.configLimit,
                currentConfig: 0,
                status: 'PENDING',
            });
           newMemberPackage = await this.memberPackageRepo.save(memberPackage);
        }

        // 5. Trả kết quả
        return {
            ...savedTransaction,
            memberPackage: newMemberPackage,
            clientSecret: dto.clientSecret ?? '',
            refId: dto.refId ?? '',   
        };
    }


    async findOne(id: string) {
        return this.paymentTransactionRepo.findOne(id);
    }

    async findPagination(body: PaymentTransactionListDto) {
        const { code, memberId, orderId, status, ...pageRequest } = body;
        const where: any = {}
        if (code) {
            where.code = code;
        }
        if (memberId) {
            where.memberId = memberId; // Update lại dùng memberContextSession 
        }
        if (orderId) {

            where.orderId = orderId;
        }
        if (status) {
            where.status = status;
        }
        return this.paymentTransactionRepo.findPagination({ where, order: { createdDate: 'DESC' } }, pageRequest);
    }

    /**
    * 
    * @param amount 
    * @param currency 
    * @returns 
    */
    async createPaymentIntent(body: PaymentIntentDto) {
        const { amount, currency, orderId, transactionId, paymentType } = body;
        return await this.stripe.paymentIntents.create({
            amount, // amount in cents
            currency,
            automatic_payment_methods: { enabled: true },
            metadata: {
                orderId,
                transactionId,
                paymentType
            },
        })
    }

    /**
     * 
     * @param orderId 
     * @param transactionId 
     * @param customerEmail 
     * @returns 
     */
    async createSubscription(body: PaymentSubscriptionDto) {
        const { orderId, transactionId, customerEmail, planId } = body;
        const plan = await this.packagePlanRepo.findOne({ where: { id: planId } });
        if (!plan) throw new NotFoundException('Plan not found');

        const customers = await this.stripe.customers.list({ email: customerEmail, limit: 1 });
        const customer = customers.data.length > 0
            ? customers.data[0]
            : await this.stripe.customers.create({ email: customerEmail });

        const subscription = await this.stripe.subscriptions.create({
            customer: customer.id,
            items: [{ price: plan.stripePriceId }],
            metadata: {
                orderId,
                transactionId,
            },
            payment_behavior: 'default_incomplete',
            expand: ['latest_invoice.payment_intent'],
        });

        return subscription;
    }
}