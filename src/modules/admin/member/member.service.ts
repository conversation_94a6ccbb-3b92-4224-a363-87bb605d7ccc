import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MemberPackageRepo, MemberRepo, OrderRepo, PackagePlanRepo } from '~/domains/primary';
import { MemberListDto } from './dto/member.dto';
import { PageRequest } from '~/@systems/utils';

@Injectable()
export class MemberService {
  constructor() {}

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  async findAll(queries: MemberListDto) {
    const { email, fullName, status, ...pageRequest } = queries;
    const where: any = {};
    if (email) {
      where.email = email;
    }
    if (fullName) {
      where.fullName = fullName;
    }
    if (status) {
      where.status = status;
    }
    return this.memberRepo.findPagination({ where }, pageRequest);
  }

  async getPackagesDetail(body: any) {
    const { id, pageSize = 10, pageIndex = 1 } = body;
    if (!id) throw new Error('Missing member id');

    const [data, total]: any = await this.memberPackageRepo.findAndCount({
      where: { memberId: id },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });

    const packagePlanIds = [...new Set(data.map(pkg => pkg.packagePlanId))];

    const packagePlans = await this.packagePlanRepo.findByIds(packagePlanIds);

    const packagePlanMap = new Map(packagePlans.map(plan => [plan.id, plan]));

    for (let item of data) {
      item.packageDetails = packagePlanMap.get(item.packagePlanId);
    }

    return { data, total };
  }

  async getOrdersDetail(body: any) {
    const { id, pageSize = 10, pageIndex = 1 } = body;
    if (!id) throw new Error('Missing member id');
    const [data, total] = await this.orderRepo.findAndCount({
      where: { memberId: id },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });
    return { data, total };
  }
}
